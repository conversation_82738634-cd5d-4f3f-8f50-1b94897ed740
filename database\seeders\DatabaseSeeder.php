<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use App\Models\School\System\School;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Database\Seeders\assessment\SchedulesSeeder;
use Database\Seeders\assessment\career\TaskAssignmentsSeeder;
use Database\Seeders\assessment\career\AnswersSeeder;
use Database\Seeders\assessment\competency\AssignmentsSeeder as CompetencyAssignmentsSeeder;
use Database\Seeders\assessment\competency\AnswersSeeder as CompetencyAnswersSeeder;
use Database\Seeders\assessment\capability\AssignmentsSeeder as CapabilityAssignmentsSeeder;
use Database\Seeders\assessment\capability\AnswersSeeder as CapabilityAnswersSeeder;
use Database\Seeders\assessment\psychology\TaskAssignmentsSeeder as PsychologyAssignmentsSeeder;
use Database\Seeders\assessment\psychology\AnswersSeeder as PsychologyAnswersSeeder;
use Database\Seeders\assessment\subject\TaskAssignmentsSeeder as SubjectAssignmentsSeeder;
use Database\Seeders\assessment\subject\AnswersSeeder as SubjectAnswersSeeder;
class DatabaseSeeder extends Seeder
{
    protected string $connect = 'mysql_prod';
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // 将内存限制设置为 512MB
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 8000);

//        // 1.1查询老库所有学校ID(跑SchoolSeeder数据，使用老库学校数据)
        $schoolIds = \DB::connection($this->connect)->table('school')
            ->where('step', '>=', 0)
            ->where('date_due', '>=', date('Y-m-d'))
            ->orderBy('id', 'asc')
            ->pluck('id')->toArray();

//        // 1.2查询新库所有学校ID(跑其他seeder数据，使用新库学校数据)
//        $schoolIds = School::where('status', 1)
////            ->where('id', '<=',200)
//            ->orderBy('id', 'asc')
////            ->skip(0)->take(200)
//            ->pluck('id')->toArray();

        //$schoolIds = [761,763,886,889,965,966,973,979,987];//学科只有这几个学校
        //$schoolIds = [508,761,763,842,886,889,913,965,966];//创新人才核心素养测评只有这几个学校

        // 单个学校
//        $schoolIds = [761];


        // 查看当前数据库
        $dbName = DB::connection()->getDatabaseName();
        if ($dbName === 'shengya_yishengya') {
            // 总体开始时间
            $totalStartTime = microtime(true);
            $totalStartTimeStr = date('Y-m-d H:i:s');

            echo "=================================================================\n";
            echo "数据迁移总体开始时间：{$totalStartTimeStr}\n";
            echo "学校总数：" . count($schoolIds) . "\n";
            echo "=================================================================\n\n";

            $seeders = $this->getSeeders();
            $seederStats = []; // 存储各个seeder的统计信息

            foreach ($seeders as $seederIndex => $seeder) {
                $seederStartTime = microtime(true);
                $seederStartTimeStr = date('Y-m-d H:i:s');

                echo "【{$seeder}】开始执行：{$seederStartTimeStr}\n";
                echo "进度：" . ($seederIndex + 1) . "/" . count($seeders) . "\n";
                echo "-----------------------------------------------------------------\n";

                $schoolStats = []; // 存储当前seeder各学校的统计信息
                $num = 1;

                foreach ($schoolIds as $schoolId) {
                    $schoolStartTime = microtime(true);
                    $schoolStartTimeStr = date('Y-m-d H:i:s');

                    Log::info("第{$num}个学校（ID：{$schoolId}） {$seeder} 数据迁移start：{$schoolStartTimeStr}");
                    echo "  第{$num}个学校（ID：{$schoolId}）开始：{$schoolStartTimeStr}";

                    $s = new $seeder($schoolId);
                    $s->run();

                    $schoolEndTime = microtime(true);
                    $schoolEndTimeStr = date('Y-m-d H:i:s');
                    $schoolDuration = $schoolEndTime - $schoolStartTime;

                    echo " => 结束：{$schoolEndTimeStr} (耗时：" . $this->formatDuration($schoolDuration) . ")\n";
                    Log::info("第{$num}个学校（ID：{$schoolId}） {$seeder} 数据迁移end：{$schoolEndTimeStr} 耗时：" . $this->formatDuration($schoolDuration));

                    $schoolStats[] = [
                        'school_id' => $schoolId,
                        'duration' => $schoolDuration,
                        'start_time' => $schoolStartTimeStr,
                        'end_time' => $schoolEndTimeStr
                    ];

                    $num++;
                }

                $seederEndTime = microtime(true);
                $seederEndTimeStr = date('Y-m-d H:i:s');
                $seederDuration = $seederEndTime - $seederStartTime;

                // 计算当前seeder的统计信息
                $avgDuration = array_sum(array_column($schoolStats, 'duration')) / count($schoolStats);
                $maxDuration = max(array_column($schoolStats, 'duration'));
                $minDuration = min(array_column($schoolStats, 'duration'));

                $seederStats[] = [
                    'seeder' => $seeder,
                    'total_duration' => $seederDuration,
                    'avg_duration_per_school' => $avgDuration,
                    'max_duration_per_school' => $maxDuration,
                    'min_duration_per_school' => $minDuration,
                    'start_time' => $seederStartTimeStr,
                    'end_time' => $seederEndTimeStr,
                    'school_count' => count($schoolStats),
                    'school_stats' => $schoolStats
                ];

                echo "-----------------------------------------------------------------\n";
                echo "【{$seeder}】执行完毕：{$seederEndTimeStr}\n";
                echo "总耗时：" . $this->formatDuration($seederDuration) . "\n";
                echo "平均每校耗时：" . $this->formatDuration($avgDuration) . "\n";
                echo "最长单校耗时：" . $this->formatDuration($maxDuration) . "\n";
                echo "最短单校耗时：" . $this->formatDuration($minDuration) . "\n";
                echo "=================================================================\n\n";
            }

            // 总体结束时间和统计
            $totalEndTime = microtime(true);
            $totalEndTimeStr = date('Y-m-d H:i:s');
            $totalDuration = $totalEndTime - $totalStartTime;

            $this->printFinalStatistics($totalStartTimeStr, $totalEndTimeStr, $totalDuration, $seederStats, count($schoolIds));
        }
    }

    /**
     * 获取所有数据填充类
     *
     * @return array
     */
    protected function getSeeders(): array
    {
        return [
            // 基础数据
            // 跑SchoolSeeder数据，使用老库学校数据，其他数据使用新库数据
            // SchoolSeeder::class,
            // RoleSeeder::class, // RoleSeeder 的简化版
            // ClassSeeder::class, // ClassSeeder执行完毕后，手动执行：数据迁移：班级数据处理.sql
            // CourseSeeder::class,
            // StudentSeeder::class, // 跑StudentSeeder数据之前，数据预处理：在原库创建ysy_student_convert临时表，将转换后的学生和账号信息通过sql（转换多条Student数据为一条）导入到该表；
            // StudentClassRelationSeeder::class,
            // TeacherSeeder::class,
            // TeacherViewClassSeeder::class,

            // 学生老师用户账号跑完后， 再手动跑：数据迁移：插入用户角色关系.sql

//
//            // 测评 - 增量执行
            SchedulesSeeder::class,//此seeder跑完后，需要将assessment_schedules表和assessment_tasks表复制到老系统中并加前缀ysy_
          //  TaskAssignmentsSeeder::class, // 生涯测评任务分配
           // AnswersSeeder::class, // 生涯测评答案
//            CompetencyAssignmentsSeeder::class, // 核心素养测评任务分配
//            CompetencyAnswersSeeder::class, // 核心素养测评答案
//            CapabilityAssignmentsSeeder::class, // 能力测评任务分配
//            CapabilityAnswersSeeder::class, // 能力测评答案
//            PsychologyAssignmentsSeeder::class, // 心理测评任务分配
//            PsychologyAnswersSeeder::class, // 心理测评答案
//            SubjectAssignmentsSeeder::class, // 学科测评任务分配
//            SubjectAnswersSeeder::class, // 学科测评答案


        ];
    }

    /**
     * 格式化时间长度
     *
     * @param float $seconds
     * @return string
     */
    private function formatDuration(float $seconds): string
    {
        if ($seconds < 60) {
            return sprintf('%.2f秒', $seconds);
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return sprintf('%d分%.2f秒', $minutes, $remainingSeconds);
        } else {
            $hours = floor($seconds / 3600);
            $minutes = floor(($seconds % 3600) / 60);
            $remainingSeconds = $seconds % 60;
            return sprintf('%d小时%d分%.2f秒', $hours, $minutes, $remainingSeconds);
        }
    }

    /**
     * 打印最终统计信息
     *
     * @param string $totalStartTime
     * @param string $totalEndTime
     * @param float $totalDuration
     * @param array $seederStats
     * @param int $schoolCount
     */
    private function printFinalStatistics(string $totalStartTime, string $totalEndTime, float $totalDuration, array $seederStats, int $schoolCount): void
    {
        echo "=================================================================\n";
        echo "                        最终统计报告                              \n";
        echo "=================================================================\n";
        echo "总体开始时间：{$totalStartTime}\n";
        echo "总体结束时间：{$totalEndTime}\n";
        echo "总体耗时：" . $this->formatDuration($totalDuration) . "\n";
        echo "学校总数：{$schoolCount}\n";
        echo "Seeder总数：" . count($seederStats) . "\n";
        echo "\n";

        // 各个Seeder的详细统计
        echo "各Seeder详细统计：\n";
        echo "-----------------------------------------------------------------\n";
        foreach ($seederStats as $index => $stat) {
            $seederName = basename(str_replace('\\', '/', $stat['seeder']));
            echo sprintf("%-3d. %-30s 总耗时：%-15s 平均每校：%-10s\n",
                $index + 1,
                $seederName,
                $this->formatDuration($stat['total_duration']),
                $this->formatDuration($stat['avg_duration_per_school'])
            );
        }

        echo "\n";
        echo "Seeder耗时排行（从高到低）：\n";
        echo "-----------------------------------------------------------------\n";

        // 按总耗时排序
        usort($seederStats, function($a, $b) {
            return $b['total_duration'] <=> $a['total_duration'];
        });

        foreach ($seederStats as $index => $stat) {
            $seederName = basename(str_replace('\\', '/', $stat['seeder']));
            $percentage = ($stat['total_duration'] / $totalDuration) * 100;
            echo sprintf("%-3d. %-30s %-15s (%.1f%%)\n",
                $index + 1,
                $seederName,
                $this->formatDuration($stat['total_duration']),
                $percentage
            );
        }

        echo "\n";
        echo "平均每校耗时最长的Seeder：\n";
        echo "-----------------------------------------------------------------\n";

        // 按平均每校耗时排序
        usort($seederStats, function($a, $b) {
            return $b['avg_duration_per_school'] <=> $a['avg_duration_per_school'];
        });

        foreach (array_slice($seederStats, 0, 5) as $index => $stat) {
            $seederName = basename(str_replace('\\', '/', $stat['seeder']));
            echo sprintf("%-3d. %-30s %-15s\n",
                $index + 1,
                $seederName,
                $this->formatDuration($stat['avg_duration_per_school'])
            );
        }

        echo "\n";
        echo "性能分析：\n";
        echo "-----------------------------------------------------------------\n";
        $totalSeederTime = array_sum(array_column($seederStats, 'total_duration'));
        $avgTimePerSchool = $totalDuration / $schoolCount;
        $avgTimePerSeeder = $totalDuration / count($seederStats);

        echo "总Seeder执行时间：" . $this->formatDuration($totalSeederTime) . "\n";
        echo "平均每校总耗时：" . $this->formatDuration($avgTimePerSchool) . "\n";
        echo "平均每个Seeder耗时：" . $this->formatDuration($avgTimePerSeeder) . "\n";

        if ($schoolCount > 1) {
            echo "预估单校完整迁移耗时：" . $this->formatDuration($avgTimePerSchool) . "\n";
        }

        echo "\n=================================================================\n";
        echo "数据迁移完成！\n";
        echo "=================================================================\n";

        // 记录到日志
        Log::info("数据迁移完成统计", [
            'total_start_time' => $totalStartTime,
            'total_end_time' => $totalEndTime,
            'total_duration' => $totalDuration,
            'school_count' => $schoolCount,
            'seeder_count' => count($seederStats),
            'seeder_stats' => $seederStats
        ]);
    }
}
