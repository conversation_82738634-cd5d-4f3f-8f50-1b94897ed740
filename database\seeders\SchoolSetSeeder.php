<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SchoolSetSeeder extends Seeder
{
    

    /**
     * Run the database seeds.
     * 根据menu_conversion表的buy_modules字段，将对应的菜单数据插入到organization_has_menus表中
     */
    public function run(): void
    {
        $startTime = microtime(true);
        Log::info("开始执行 SchoolSetSeeder");
        echo "开始执行 SchoolSetSeeder\n";

        // 设置内存和时间限制
        ini_set('memory_limit', '1024M');
        ini_set('max_execution_time', 3600);

        try {
            // 先测试基本功能
            echo "测试数据库连接...\n";
            $testCount = DB::table('menu_conversion')->count();
            echo "menu_conversion 表总记录数: {$testCount}\n";

            echo "正在查询 menu_conversion 表数据...\n";
            // 获取menu_conversion表的数据，先限制数量进行测试
            $menuConversions = DB::table('menu_conversion')
                ->whereNotNull('buy_modules')
                ->where('buy_modules', '!=', '')
                ->limit(5) // 先只处理5条记录进行测试
                ->get();

            echo "找到 " . $menuConversions->count() . " 条记录\n";

            if ($menuConversions->isEmpty()) {
                Log::info("menu_conversion表中没有找到有效的buy_modules数据");
                echo "menu_conversion表中没有找到有效的buy_modules数据\n";
                return;
            }

            DB::beginTransaction();

            $totalProcessed = 0;
            $totalInserted = 0;

            foreach ($menuConversions as $conversion) {
                $totalProcessed++;
                echo "正在处理第 {$totalProcessed} 个学校: {$conversion->name}\n";

                // 解析buy_modules，只保留两位数的编码
                $buyModules = $this->parseBuyModules($conversion->buy_modules);
                echo "解析到的模块: " . implode(',', $buyModules) . "\n";

                if (empty($buyModules)) {
                    Log::warning("学校 {$conversion->name} 没有有效的两位数模块编码", [
                        'buy_modules' => $conversion->buy_modules
                    ]);
                    echo "警告: 学校 {$conversion->name} 没有有效的两位数模块编码\n";
                    continue;
                }

                // 根据学校名称查找organization_id
                echo "正在查找学校对应的organization_id...\n";
                $organizationId = $this->findOrganizationId($conversion->name);

                if (!$organizationId) {
                    Log::warning("未找到学校对应的organization_id", [
                        'school_name' => $conversion->name
                    ]);
                    echo "警告: 未找到学校 {$conversion->name} 对应的organization_id\n";
                    continue;
                }

                echo "找到organization_id: {$organizationId}\n";

                // 处理每个模块编码
                echo "正在处理模块编码...\n";
                $insertedCount = $this->processModulesForOrganization($organizationId, $buyModules, $conversion);
                $totalInserted += $insertedCount;

                echo "处理学校: {$conversion->name}, 插入菜单数: {$insertedCount}\n";
                Log::info("处理学校: {$conversion->name}, 插入菜单数: {$insertedCount}");
            }

            DB::commit();

            $endTime = microtime(true);
            $duration = $endTime - $startTime;

            Log::info("SchoolSetSeeder执行完成", [
                'total_processed' => $totalProcessed,
                'total_inserted' => $totalInserted,
                'duration' => round($duration, 2) . '秒'
            ]);

            echo "SchoolSetSeeder执行完成\n";
            echo "处理学校数: {$totalProcessed}\n";
            echo "插入菜单数: {$totalInserted}\n";
            echo "耗时: " . round($duration, 2) . "秒\n";

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error("SchoolSetSeeder执行失败", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * 解析buy_modules字符串，只保留两位数的编码
     */
    private function parseBuyModules(string $buyModules): array
    {
        $modules = explode(',', $buyModules);
        $validModules = [];

        foreach ($modules as $module) {
            $module = trim($module);
            // 只保留两位数的编码
            if (preg_match('/^\d{2}$/', $module)) {
                $validModules[] = $module;
            }
        }

        return array_unique($validModules);
    }

    /**
     * 根据学校名称查找organization_id
     */
    private function findOrganizationId(string $schoolName): ?int
    {
        // 首先尝试通过schools表和organizations表关联查找
        $organization = DB::table('organizations')
            ->join('schools', function($join) {
                $join->on('organizations.model_id', '=', 'schools.id')
                     ->where('organizations.model_type', '=', 'school');
            })
            ->where('schools.name', $schoolName)
            ->select('organizations.id')
            ->first();

        if ($organization) {
            return $organization->id;
        }

        // 如果没找到，尝试通过org_name字段查找
        $organization = DB::table('organizations')
            ->where('org_name', $schoolName)
            ->select('id')
            ->first();

        return $organization ? $organization->id : null;
    }

    /**
     * 为指定机构处理模块编码，插入菜单数据
     */
    private function processModulesForOrganization(int $organizationId, array $buyModules, $conversion): int
    {
        $insertedCount = 0;
        $currentTime = Carbon::now();
        
        // 获取到期日期
        $dateDue = $conversion->date_due ? Carbon::parse($conversion->date_due) : Carbon::now()->addYears(10);

        foreach ($buyModules as $moduleCode) {
            // 查找对应的菜单
            $menu = DB::table('menus')
                ->where('code', $moduleCode)
                ->where('status', 1)
                ->first();

            if (!$menu) {
                Log::warning("未找到编码为 {$moduleCode} 的菜单");
                continue;
            }

            // 收集需要插入的菜单（父级和子级）
            $menusToInsert = $this->collectMenusToInsert($menu);

            foreach ($menusToInsert as $menuToInsert) {
                // 检查是否已存在
                $exists = DB::table('organization_has_menus')
                    ->where('organization_id', $organizationId)
                    ->where('menu_id', $menuToInsert->id)
                    ->exists();

                if (!$exists) {
                    $insertData = [
                        'organization_id' => $organizationId,
                        'menu_id' => $menuToInsert->id,
                        'menu_code' => $menuToInsert->code,
                        'parent_id' => $menuToInsert->parent_id,
                        'menu_alias' => '',
                        'sort' => $menuToInsert->sort,
                        'status' => 1,
                        'date_start' => $currentTime->toDateString(),
                        'date_due' => $dateDue->toDateString(),
                        'creator' => 'SchoolSetSeeder',
                        'updater' => 'SchoolSetSeeder',
                        'created_at' => $currentTime,
                        'updated_at' => $currentTime,
                    ];

                    DB::table('organization_has_menus')->insert($insertData);
                    $insertedCount++;
                }
            }
        }

        return $insertedCount;
    }

    /**
     * 收集需要插入的菜单（包括父级和子级）
     */
    private function collectMenusToInsert($targetMenu, $processedIds = []): array
    {
        // 防止无限递归
        if (in_array($targetMenu->id, $processedIds)) {
            return [];
        }

        $processedIds[] = $targetMenu->id;
        $menusToInsert = [];

        // 添加目标菜单本身
        $menusToInsert[] = $targetMenu;

        // 如果目标菜单有父级，添加父级菜单
        if ($targetMenu->parent_id > 0 && !in_array($targetMenu->parent_id, $processedIds)) {
            $parentMenu = DB::table('menus')
                ->where('id', $targetMenu->parent_id)
                ->where('status', 1)
                ->first();

            if ($parentMenu) {
                // 递归添加所有父级菜单
                $parentMenus = $this->collectMenusToInsert($parentMenu, $processedIds);
                $menusToInsert = array_merge($parentMenus, $menusToInsert);
            }
        }

        // 添加所有子级菜单
        $childMenus = DB::table('menus')
            ->where('parent_id', $targetMenu->id)
            ->where('status', 1)
            ->get();

        foreach ($childMenus as $childMenu) {
            if (!in_array($childMenu->id, $processedIds)) {
                $childMenusToInsert = $this->collectMenusToInsert($childMenu, $processedIds);
                $menusToInsert = array_merge($menusToInsert, $childMenusToInsert);
            }
        }

        // 去重（基于菜单ID）
        $uniqueMenus = [];
        $seenIds = [];

        foreach ($menusToInsert as $menu) {
            if (!in_array($menu->id, $seenIds)) {
                $uniqueMenus[] = $menu;
                $seenIds[] = $menu->id;
            }
        }

        return $uniqueMenus;
    }
}
