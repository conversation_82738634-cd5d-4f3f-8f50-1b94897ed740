<?php

namespace Database\Seeders\assessment\competency;

use Database\Seeders\BaseGlobalSeeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

ini_set('memory_limit', '2048M');

class IncrementalAssignmentsSeeder extends BaseGlobalSeeder
{
    protected string $assessment_type = 'competency';

    // 核心素养测评的映射关系
    private const SURVEYIDTOASSESSMENTID = [
        // 根据实际的核心素养测评ID进行配置
        // 示例：
        // 9  => 9,
        // 10 => 10,
        // 等等...
    ];

    public function __construct($schoolId)
    {
        parent::__construct($schoolId);
    }

    protected function getSurveyIds(): array
    {
        // 返回核心素养测评的 survey_id 数组
        return array_keys(self::SURVEYIDTOASSESSMENTID);
    }

    protected function getAssessmentIds(): array
    {
        // 返回核心素养测评的 assessment_id 数组
        return array_values(self::SURVEYIDTOASSESSMENTID);
    }

    protected function executeSeeder(): void
    {
        $lastProcessedId = $this->getLastProcessedId();
        $assessmentIds = $this->getAssessmentIds();
        $surveyIds = $this->getSurveyIds();

        if (empty($surveyIds)) {
            Log::info('核心素养测评配置为空，跳过执行', [
                'school_id' => $this->school_id
            ]);
            return;
        }

        // 使用基类提供的增量查询方法（自动包含 > $lastProcessedId 条件）
        $studentSurveyList = $this->getIncrementalAssignmentQuery()
            ->select([
                'tasks.id as task_id',
                'tasks.assessment_id',
                'session.session_id',
                'session.member_id as student_member_id',
                'session.student_id',
                'session.create_time',
                'session.used_time',
                'session.result',
                'session.survey_id',
                'session.pdf_url',
                'session.id as survey_session_id'
            ])->get();

        if ($studentSurveyList->isEmpty()) {
            Log::info('没有新的核心素养测评数据', [
                'school_id' => $this->school_id,
                'last_processed_id' => $lastProcessedId
            ]);
            return;
        }

        // 获取学生班级映射
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
            ->whereIn('old_student_id', $oldStudentIds)
            ->select(['id', 'old_student_id'])
            ->pluck('id', 'old_student_id')
            ->toArray();

        // 处理数据
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID;
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr, $surveyToAssessmentMap) {
            return [
                'old_session_id' => $item->session_id,
                'old_student_id' => $item->student_id,
                'school_id' => $this->school_id,
                'assessment_task_id' => $item->task_id,
                'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
                'student_id' => $item->student_member_id,
                'user_id' => $item->student_member_id,
                'duration' => $item->used_time,
                'results' => $item->result,
                'status' => 2,
                'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
                'pdf_url' => $item->pdf_url,
                'created_at' => $item->create_time,
            ];
        })->toArray();

        // 插入数据
        if (!empty($assignmentsData)) {
            DB::transaction(function () use ($assignmentsData) {
                collect($assignmentsData)->chunk(1000)->each(function ($chunk) {
                    DB::table('assessment_task_assignments')->insert($chunk->toArray());
                });
            });

            // 更新执行日志
            $maxSurveySessionId = $studentSurveyList->max('survey_session_id');
            $this->updateExecutionLog($maxSurveySessionId, count($assignmentsData));

            Log::info('核心素养测评任务分配完成', [
                'school_id' => $this->school_id,
                'total_records' => count($assignmentsData),
                'last_processed_survey_session_id' => $maxSurveySessionId,
            ]);
        }
    }
}
